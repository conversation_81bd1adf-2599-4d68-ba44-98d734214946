#!/bin/bash

# GameFlex Backend - Staging Deployment Script
# This script deploys the SAM application to the staging environment
# Includes support for importing existing resources

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Starting GameFlex Backend Staging Deployment..."

# Set environment to staging
export ENVIRONMENT=staging
export PROJECT_NAME=gameflex
export STACK_NAME=gameflex-staging

# Check if staging samconfig exists
if [ ! -f "samconfig-staging.toml" ]; then
    print_error "samconfig-staging.toml not found"
    echo "💡 Create it from the example or copy from samconfig.toml.example"
    echo "   Make sure it has proper staging configuration"
    exit 1
fi

print_status "Using staging configuration from samconfig-staging.toml..."

# Check if AWS credentials are configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "Prerequisites check passed"



# Function to create import resources file
create_import_file() {
    local resources=("$@")
    local import_file="import-resources-staging.json"

    print_status "Creating import file: $import_file"

    echo "[" > "$import_file"
    local first=true

    for resource in "${resources[@]}"; do
        local logical_id="${resource%%:*}"
        local resource_name="${resource##*:}"

        if [ "$first" = false ]; then
            echo "," >> "$import_file"
        fi
        first=false

        if [[ "$logical_id" == *"Secret" ]]; then
            cat >> "$import_file" << EOF
  {
    "ResourceType": "AWS::SecretsManager::Secret",
    "LogicalResourceId": "$logical_id",
    "ResourceIdentifier": {
      "Id": "$resource_name"
    }
  }
EOF
        else
            cat >> "$import_file" << EOF
  {
    "ResourceType": "AWS::DynamoDB::Table",
    "LogicalResourceId": "$logical_id",
    "ResourceIdentifier": {
      "TableName": "$resource_name"
    }
  }
EOF
        fi
    done

    echo "" >> "$import_file"
    echo "]" >> "$import_file"

    print_success "Import file created: $import_file"
}

# Function to import existing resources
import_existing_resources() {
    local resources=("$@")

    print_status "Importing existing resources into CloudFormation stack..."

    # Create import file
    create_import_file "${resources[@]}"

    # Build the template first
    print_status "Building SAM application for import..."
    sam build --config-file samconfig-staging.toml

    # Create import changeset
    print_status "Creating import changeset..."
    aws cloudformation create-change-set \
        --stack-name "$STACK_NAME" \
        --template-body file://.aws-sam/build/template.yaml \
        --change-set-name "import-existing-resources-$(date +%s)" \
        --change-set-type IMPORT \
        --resources-to-import file://import-resources-staging.json \
        --parameters ParameterKey=Environment,ParameterValue=${ENVIRONMENT} \
                    ParameterKey=ProjectName,ParameterValue=${PROJECT_NAME} \
                    ParameterKey=DomainName,ParameterValue=staging.api.gameflex.io \
                    ParameterKey=CertificateArn,ParameterValue=arn:aws:acm:us-east-1:405316604661:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662 \
                    ParameterKey=R2BucketName,ParameterValue=gameflex-staging \
                    ParameterKey=R2PublicUrl,ParameterValue=https://staging.media.gameflex.io \
        --capabilities CAPABILITY_IAM

    # Wait for changeset creation to complete
    print_status "Waiting for changeset creation to complete..."
    changeset_name=$(aws cloudformation list-change-sets --stack-name "$STACK_NAME" --query 'Summaries[0].ChangeSetName' --output text)
    aws cloudformation wait change-set-create-complete --stack-name "$STACK_NAME" --change-set-name "$changeset_name"

    # Execute the changeset
    print_status "Executing import changeset..."
    aws cloudformation execute-change-set \
        --stack-name "$STACK_NAME" \
        --change-set-name "$changeset_name"

    # Wait for import to complete
    print_status "Waiting for import to complete..."
    aws cloudformation wait stack-import-complete --stack-name "$STACK_NAME"

    print_success "Resources imported successfully!"

    # Clean up import file
    rm -f import-resources-staging.json
}

# Check if stack exists and if resources exist outside the stack
if aws cloudformation describe-stacks --stack-name "$STACK_NAME" &>/dev/null; then
    print_status "Stack already exists - will perform normal update"
    NEEDS_IMPORT=false
else
    print_status "Stack does not exist - checking for existing resources"
    # Capture existing resources in a different way
    EXISTING_RESOURCES=()

    # Check secrets
    r2_secret="${PROJECT_NAME}-r2-config-${ENVIRONMENT}"
    app_secret="${PROJECT_NAME}-app-config-${ENVIRONMENT}"

    if aws secretsmanager describe-secret --secret-id "$r2_secret" &>/dev/null; then
        print_warning "Secret exists: $r2_secret"
        EXISTING_RESOURCES+=("R2Secret:$r2_secret")
    fi

    if aws secretsmanager describe-secret --secret-id "$app_secret" &>/dev/null; then
        print_warning "Secret exists: $app_secret"
        EXISTING_RESOURCES+=("AppConfigSecret:$app_secret")
    fi

    # Check DynamoDB tables
    tables=(
        "PostsTable:${PROJECT_NAME}-${ENVIRONMENT}-Posts"
        "MediaTable:${PROJECT_NAME}-${ENVIRONMENT}-Media"
        "UserProfilesTable:${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
        "CommentsTable:${PROJECT_NAME}-${ENVIRONMENT}-Comments"
        "LikesTable:${PROJECT_NAME}-${ENVIRONMENT}-Likes"
        "FollowsTable:${PROJECT_NAME}-${ENVIRONMENT}-Follows"
        "ChannelsTable:${PROJECT_NAME}-${ENVIRONMENT}-Channels"
        "ChannelMembersTable:${PROJECT_NAME}-${ENVIRONMENT}-ChannelMembers"
        "ReflexesTable:${PROJECT_NAME}-${ENVIRONMENT}-Reflexes"
        "UsersTable:${PROJECT_NAME}-${ENVIRONMENT}-Users"
    )

    for table_info in "${tables[@]}"; do
        logical_id="${table_info%%:*}"
        table_name="${table_info##*:}"
        if aws dynamodb describe-table --table-name "$table_name" &>/dev/null; then
            print_warning "Table exists: $table_name"
            EXISTING_RESOURCES+=("$logical_id:$table_name")
        fi
    done

    if [ ${#EXISTING_RESOURCES[@]} -gt 0 ]; then
        print_warning "Found existing resources outside of CloudFormation stack"
        print_status "Will import existing resources first"
        NEEDS_IMPORT=true
    else
        print_status "No existing resources found - will perform normal deployment"
        NEEDS_IMPORT=false
    fi
fi

# Import resources if needed
if [ "$NEEDS_IMPORT" = true ]; then
    import_existing_resources "${EXISTING_RESOURCES[@]}"
fi

# Build the SAM application
print_status "Building SAM application..."
sam build --config-file samconfig-staging.toml

if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi

print_success "Build completed successfully"

# Deploy to staging
print_status "Deploying to staging environment..."
echo "   - Stack: gameflex-staging"
echo "   - Environment: staging"
echo "   - Domain: staging.api.gameflex.io"
echo "   - Media Domain: staging.media.gameflex.io"
echo ""
print_status "SAM Configuration:"
echo "   - Config File: samconfig-staging.toml"
echo "   - All parameters configured in samconfig file"
echo ""

# Deploy to staging
sam deploy --config-file samconfig-staging.toml

if [ $? -ne 0 ]; then
    print_error "Deployment failed"
    exit 1
fi

print_success "Deployment completed successfully!"

# Get the outputs
print_status "Deployment Information:"
echo "=========================="

# Get stack outputs
API_URL=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_CLIENT_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

R2_SECRET_NAME=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`R2SecretName`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

echo "API Gateway URL: $API_URL"
echo "User Pool ID: $USER_POOL_ID"
echo "User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "R2 Secret Name: $R2_SECRET_NAME"
echo ""

# Update samconfig.toml with deployment outputs
print_status "Updating samconfig.toml with deployment outputs..."
if [ -f "samconfig.toml" ]; then
    # Create a backup
    cp samconfig.toml samconfig.toml.backup

    # Update the values in samconfig.toml staging section
    if [ "$USER_POOL_ID" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^USER_POOL_ID=.*/USER_POOL_ID=$USER_POOL_ID/" samconfig.toml
    fi

    if [ "$USER_POOL_CLIENT_ID" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^USER_POOL_CLIENT_ID=.*/USER_POOL_CLIENT_ID=$USER_POOL_CLIENT_ID/" samconfig.toml
    fi

    if [ "$R2_SECRET_NAME" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s/^R2_SECRET_NAME=.*/R2_SECRET_NAME=$R2_SECRET_NAME/" samconfig.toml
    fi

    # Update API URLs if available
    if [ "$API_URL" != "Not available" ]; then
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^API_URL_REMOTE=.*|API_URL_REMOTE=$API_URL|" samconfig.toml
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^API_BASE_URL=.*|API_BASE_URL=$API_URL|" samconfig.toml
        sed -i "/\[staging\.global\.parameters\]/,/\[.*\]/ s|^TEST_API_BASE_URL=.*|TEST_API_BASE_URL=$API_URL|" samconfig.toml
    fi

    print_success "Updated samconfig.toml with deployment outputs"
    echo "   Backup saved as samconfig.toml.backup"
else
    print_warning "samconfig.toml file not found, skipping update"
fi

# Instructions for next steps
echo "🔧 Next Steps:"
echo "=============="
echo "1. Configure your DNS to point staging.api.gameflex.io to the API Gateway"
echo "2. Configure your DNS to point staging.media.gameflex.io to your CloudFlare R2 bucket"
echo "3. Update the R2 secret in AWS Secrets Manager with your CloudFlare R2 credentials:"
echo "   aws secretsmanager put-secret-value \\"
echo "     --secret-id '$R2_SECRET_NAME' \\"
echo "     --secret-string '{\"accountId\":\"YOUR_R2_ACCOUNT_ID\",\"accessKeyId\":\"YOUR_R2_ACCESS_KEY\",\"secretAccessKey\":\"YOUR_R2_SECRET_KEY\",\"endpoint\":\"https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com\",\"bucketName\":\"gameflex-staging\",\"publicUrl\":\"https://staging.media.gameflex.io\"}'"
echo ""
echo "4. Test the deployment:"
echo "   curl $API_URL/health"
echo ""
echo "🎉 Staging deployment complete!"

[{"ResourceType": "AWS::<PERSON>Manager::Secret", "LogicalResourceId": "R2Secret", "ResourceIdentifier": {"Id": "gameflex-r2-config-staging"}}, {"ResourceType": "AWS::<PERSON>Manager::Secret", "LogicalResourceId": "AppConfigSecret", "ResourceIdentifier": {"Id": "gameflex-app-config-staging"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "PostsTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Posts"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "MediaTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Media"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "UserProfilesTable", "ResourceIdentifier": {"TableName": "gameflex-staging-UserProfiles"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "CommentsTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Comments"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "LikesTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Likes"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "FollowsTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Follows"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "ChannelsTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Channels"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "ChannelMembersTable", "ResourceIdentifier": {"TableName": "gameflex-staging-ChannelMembers"}}, {"ResourceType": "AWS::DynamoDB::Table", "LogicalResourceId": "ReflexesTable", "ResourceIdentifier": {"TableName": "gameflex-staging-Reflexes"}}]
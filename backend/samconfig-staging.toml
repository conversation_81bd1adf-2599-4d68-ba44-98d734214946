version = 0.1

# Staging Environment Configuration
[default.deploy.parameters]
stack_name = "gameflex-staging"
resolve_s3 = true
s3_prefix = "gameflex-staging"
region = "us-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"staging\" ProjectName=\"gameflex\" DomainName=\"staging.api.gameflex.io\" CertificateArn=\"arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662\" R2AccountId=\"\" R2AccessKeyId=\"\" R2SecretAccessKey=\"\" R2Endpoint=\"\" R2BucketName=\"gameflex-staging\" R2PublicUrl=\"https://staging.media.gameflex.io\""
image_repositories = []

[default.build.parameters]
cached = true
parallel = true

# Staging Environment Variables
[default.global.parameters]
environment_variables = """
ENVIRONMENT=staging
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://staging.api.gameflex.io
API_BASE_URL=https://staging.api.gameflex.io
DEBUG=0
USER_POOL_ID=
USER_POOL_CLIENT_ID=
USERS_TABLE=gameflex-staging-Users
POSTS_TABLE=gameflex-staging-Posts
MEDIA_TABLE=gameflex-staging-Media
USER_PROFILES_TABLE=gameflex-staging-UserProfiles
COMMENTS_TABLE=gameflex-staging-Comments
LIKES_TABLE=gameflex-staging-Likes
FOLLOWS_TABLE=gameflex-staging-Follows
CHANNELS_TABLE=gameflex-staging-Channels
CHANNEL_MEMBERS_TABLE=gameflex-staging-ChannelMembers
REFLEXES_TABLE=gameflex-staging-Reflexes
R2_SECRET_NAME=gameflex-r2-config-staging
APP_CONFIG_SECRET_NAME=gameflex-app-config-staging
DOMAIN_NAME=staging.api.gameflex.io
MEDIA_DOMAIN=staging.media.gameflex.io
SSL_ENABLED=true
FORCE_HTTPS=true
"""

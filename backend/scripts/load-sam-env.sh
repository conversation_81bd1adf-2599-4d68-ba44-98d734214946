#!/bin/bash

# Load environment variables from samconfig.toml
# Usage: source scripts/load-sam-env.sh [environment]

# Get the environment (default to development)
LOAD_ENV=${1:-${ENVIRONMENT:-development}}

# Map environment names to samconfig.toml sections
case "$LOAD_ENV" in
    "development")
        CONFIG_SECTION="default"
        ;;
    "staging")
        CONFIG_SECTION="staging"
        ;;
    "production")
        CONFIG_SECTION="production"
        ;;
    *)
        echo "❌ Unknown environment: $LOAD_ENV"
        echo "   Valid environments: development, staging, production"
        return 1 2>/dev/null || exit 1
        ;;
esac

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"
SAMCONFIG_FILE="$BACKEND_DIR/samconfig-$LOAD_ENV.toml"

# Check if samconfig.toml exists
if [ ! -f "$SAMCONFIG_FILE" ]; then
    echo "❌ samconfig-$LOAD_ENV.toml not found at $SAMCONFIG_FILE"
    if [ -f "$BACKEND_DIR/samconfig.toml.example" ]; then
        echo "💡 Found samconfig-$LOAD_ENV.toml.example. Copy it to samconfig-$LOAD_ENV.toml and update with your values:"
        echo "   cp samconfig-$LOAD_ENV.toml.example samconfig.toml"
        echo "   # Then edit samconfig-$LOAD_ENV.toml with your actual credentials"
    fi
    return 1 2>/dev/null || exit 1
fi

# Function to extract environment variables from samconfig.toml
extract_env_vars() {
    local section="$1"
    local in_section=false
    local in_env_vars=false

    while IFS= read -r line; do
        # Check if we're entering the target section
        if [[ "$line" =~ ^\[${section}\.global\.parameters\] ]]; then
            in_section=true
            continue
        fi

        # Check if we're leaving the section
        if [[ "$line" =~ ^\[.*\] ]] && [ "$in_section" = true ]; then
            break
        fi

        # Check if we're in the environment_variables block
        if [ "$in_section" = true ] && [[ "$line" =~ ^environment_variables.*= ]]; then
            in_env_vars=true
            continue
        fi

        # Check if we're leaving the environment_variables block
        if [ "$in_env_vars" = true ] && [[ "$line" =~ ^\"\"\"$ ]]; then
            break
        fi

        # Extract environment variables
        if [ "$in_env_vars" = true ] && [[ "$line" =~ ^[A-Z_].*= ]]; then
            # Clean up the line and export the variable
            clean_line=$(echo "$line" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            if [ -n "$clean_line" ]; then
                export "$clean_line"
            fi
        fi
    done < "$SAMCONFIG_FILE"
}

# Load environment variables
echo "📋 Loading environment variables from samconfig-$LOAD_ENV.toml [$CONFIG_SECTION]..."
extract_env_vars "$CONFIG_SECTION"

# Verify some key variables were loaded
if [ -n "$ENVIRONMENT" ] && [ -n "$PROJECT_NAME" ]; then
    echo "✅ Environment variables loaded successfully"
    echo "   Environment: $ENVIRONMENT"
    echo "   Project: $PROJECT_NAME"
    echo "   Region: ${AWS_REGION:-not set}"
else
    echo "⚠️  Warning: Some environment variables may not have loaded correctly"
    echo "   ENVIRONMENT: ${ENVIRONMENT:-not set}"
    echo "   PROJECT_NAME: ${PROJECT_NAME:-not set}"
fi
